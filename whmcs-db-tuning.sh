#!/bin/bash

# WHMCS Database Performance Tuning Script
# This script provides additional database optimizations for WHMCS
# Run this after the main installation to fine-tune performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

get_system_memory() {
    # Get total system memory in MB
    local mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_mb=$((mem_kb / 1024))
    echo $mem_mb
}

calculate_innodb_buffer_pool() {
    local total_mem=$1
    # Use 70% of total memory for InnoDB buffer pool, but cap at reasonable limits
    local buffer_pool=$((total_mem * 70 / 100))
    
    # Minimum 512MB, maximum 80% of total memory
    if [ $buffer_pool -lt 512 ]; then
        buffer_pool=512
    elif [ $buffer_pool -gt $((total_mem * 80 / 100)) ]; then
        buffer_pool=$((total_mem * 80 / 100))
    fi
    
    echo $buffer_pool
}

optimize_database_config() {
    log_info "Optimizing MariaDB configuration based on system resources..."
    
    local total_mem=$(get_system_memory)
    local innodb_buffer_pool=$(calculate_innodb_buffer_pool $total_mem)
    local innodb_log_file_size=$((innodb_buffer_pool / 4))
    local query_cache_size=$((total_mem / 8))
    
    # Cap query cache at 512MB
    if [ $query_cache_size -gt 512 ]; then
        query_cache_size=512
    fi
    
    log_info "System Memory: ${total_mem}MB"
    log_info "InnoDB Buffer Pool: ${innodb_buffer_pool}MB"
    log_info "Query Cache: ${query_cache_size}MB"
    
    # Backup existing configuration
    cp /etc/mysql/mariadb.conf.d/99-whmcs-optimizations.cnf /etc/mysql/mariadb.conf.d/99-whmcs-optimizations.cnf.backup
    
    # Create optimized configuration
    cat > /etc/mysql/mariadb.conf.d/99-whmcs-optimizations.cnf <<EOF
[mysqld]
# Basic settings
max_connections = 300
thread_cache_size = 100
table_open_cache = 8000
table_definition_cache = 4000
open_files_limit = 65535

# InnoDB settings optimized for system memory
innodb_buffer_pool_size = ${innodb_buffer_pool}M
innodb_buffer_pool_instances = $(( (innodb_buffer_pool + 1023) / 1024 ))
innodb_log_file_size = ${innodb_log_file_size}M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_io_capacity = 1000
innodb_io_capacity_max = 2000
innodb_purge_threads = 4
innodb_page_cleaners = 4

# Additional InnoDB optimizations
innodb_adaptive_hash_index = 1
innodb_change_buffering = all
innodb_old_blocks_time = 1000
innodb_stats_on_metadata = 0
innodb_lock_wait_timeout = 120

# Query cache optimized for WHMCS read patterns
query_cache_type = 1
query_cache_size = ${query_cache_size}M
query_cache_limit = 4M
query_cache_min_res_unit = 2k

# Temporary tables for complex WHMCS queries
tmp_table_size = 256M
max_heap_table_size = 256M

# MyISAM settings (some WHMCS modules may use MyISAM)
key_buffer_size = 256M
myisam_sort_buffer_size = 128M
myisam_repair_threads = 1

# Sort and join optimizations
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 4M
bulk_insert_buffer_size = 64M

# Binary logging for replication/backup
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M
sync_binlog = 0

# Slow query log for optimization
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1
min_examined_row_limit = 1000

# Character set for WHMCS
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# Security settings
local_infile = 0
skip_show_database

# Network settings
max_allowed_packet = 64M
connect_timeout = 10
wait_timeout = 600
interactive_timeout = 600
net_read_timeout = 30
net_write_timeout = 60

# Thread and connection handling
thread_handling = pool-of-threads
thread_pool_size = 32
thread_pool_max_threads = 2000

# Performance schema (disable for production to save memory)
performance_schema = OFF

[mysql]
default_character_set = utf8mb4

[mysqldump]
default_character_set = utf8mb4
single_transaction = 1
routines = 1
triggers = 1

[client]
default_character_set = utf8mb4
EOF

    log_info "Database configuration optimized for ${total_mem}MB system"
}

create_maintenance_scripts() {
    log_info "Creating database maintenance scripts..."
    
    # Create database optimization script
    cat > /usr/local/bin/whmcs-db-optimize <<'EOF'
#!/bin/bash
# WHMCS Database Optimization Script

DB_USER="root"
DB_PASS=""
MYSQL_ROOT_PASS=""

# Get database credentials
if [ -f "/root/whmcs-db-credentials.txt" ]; then
    source /root/whmcs-db-credentials.txt
    # Use root password if available
    if [ -n "$MYSQL_ROOT_PASS" ]; then
        DB_PASS="$MYSQL_ROOT_PASS"
    fi
fi

echo "Starting WHMCS database optimization..."

# Optimize all tables in WHMCS database
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "
USE ${DB_NAME};
OPTIMIZE TABLE tblactivitylog;
OPTIMIZE TABLE tblclientssummary;
OPTIMIZE TABLE tblinvoices;
OPTIMIZE TABLE tblinvoiceitems;
OPTIMIZE TABLE tblorders;
OPTIMIZE TABLE tbltickets;
OPTIMIZE TABLE tblticketreplies;
OPTIMIZE TABLE tbldomains;
OPTIMIZE TABLE tblhosting;
OPTIMIZE TABLE tblaccounts;
OPTIMIZE TABLE tblemailtemplates;
OPTIMIZE TABLE tblconfiguration;
"

echo "Database optimization completed."

# Show table status
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "
USE ${DB_NAME};
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows
FROM information_schema.TABLES 
WHERE table_schema = '${DB_NAME}'
ORDER BY (data_length + index_length) DESC
LIMIT 10;
"
EOF

    chmod +x /usr/local/bin/whmcs-db-optimize
    
    # Create backup script
    cat > /usr/local/bin/whmcs-db-backup <<'EOF'
#!/bin/bash
# WHMCS Database Backup Script

BACKUP_DIR="/var/backups/whmcs"
RETENTION_DAYS=30
DB_USER="root"
DB_PASS=""
MYSQL_ROOT_PASS=""

# Get database credentials
if [ -f "/root/whmcs-db-credentials.txt" ]; then
    source /root/whmcs-db-credentials.txt
    # Use root password if available
    if [ -n "$MYSQL_ROOT_PASS" ]; then
        DB_PASS="$MYSQL_ROOT_PASS"
    fi
else
    echo "Error: Database credentials file not found"
    exit 1
fi

# Create backup directory
mkdir -p $BACKUP_DIR

# Create backup filename with timestamp
BACKUP_FILE="$BACKUP_DIR/whmcs_backup_$(date +%Y%m%d_%H%M%S).sql.gz"

echo "Creating WHMCS database backup..."

# Create compressed backup
mysqldump --single-transaction --routines --triggers \
    -u${DB_USER} ${DB_PASS:+-p$DB_PASS} ${DB_NAME} | gzip > $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "Backup created successfully: $BACKUP_FILE"
    
    # Remove old backups
    find $BACKUP_DIR -name "whmcs_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete
    echo "Old backups cleaned up (retention: $RETENTION_DAYS days)"
else
    echo "Backup failed!"
    exit 1
fi
EOF

    chmod +x /usr/local/bin/whmcs-db-backup
    
    # Create performance monitoring script
    cat > /usr/local/bin/whmcs-db-monitor <<'EOF'
#!/bin/bash
# WHMCS Database Performance Monitor

DB_USER="root"
DB_PASS=""
MYSQL_ROOT_PASS=""

# Get database credentials
if [ -f "/root/whmcs-db-credentials.txt" ]; then
    source /root/whmcs-db-credentials.txt
    # Use root password if available
    if [ -n "$MYSQL_ROOT_PASS" ]; then
        DB_PASS="$MYSQL_ROOT_PASS"
    fi
fi

echo "=== WHMCS Database Performance Report ==="
echo "Generated: $(date)"
echo

# Show current connections
echo "=== Current Connections ==="
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "SHOW STATUS LIKE 'Threads_connected';"
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "SHOW STATUS LIKE 'Max_used_connections';"
echo

# Show InnoDB status
echo "=== InnoDB Buffer Pool Usage ==="
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "
SELECT 
    ROUND((PagesData*PageSize)/POWER(1024,3),2) AS DataGB,
    ROUND((PagesFree*PageSize)/POWER(1024,3),2) AS FreeGB,
    ROUND((PagesData*PageSize)/POWER(1024,3)/(PagesData+PagesFree)*100,2) AS PercentUsed
FROM (
    SELECT 
        variable_value AS PageSize
    FROM information_schema.global_status
    WHERE variable_name = 'Innodb_page_size'
) AS ps
JOIN (
    SELECT 
        variable_value AS PagesData
    FROM information_schema.global_status
    WHERE variable_name = 'Innodb_buffer_pool_pages_data'
) AS pd
JOIN (
    SELECT 
        variable_value AS PagesFree  
    FROM information_schema.global_status
    WHERE variable_name = 'Innodb_buffer_pool_pages_free'
) AS pf;
"

# Show slow queries
echo "=== Slow Query Statistics ==="
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "SHOW STATUS LIKE 'Slow_queries';"
echo

# Show query cache statistics
echo "=== Query Cache Statistics ==="
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "
SHOW STATUS LIKE 'Qcache%';
"
echo

# Show top tables by size
echo "=== Largest Tables ==="
mysql -u${DB_USER} ${DB_PASS:+-p$DB_PASS} -e "
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
    table_rows
FROM information_schema.TABLES 
WHERE table_schema = '${DB_NAME}'
ORDER BY (data_length + index_length) DESC
LIMIT 5;
"
EOF

    chmod +x /usr/local/bin/whmcs-db-monitor
    
    log_info "Maintenance scripts created:"
    log_info "  - /usr/local/bin/whmcs-db-optimize (optimize database tables)"
    log_info "  - /usr/local/bin/whmcs-db-backup (create database backup)"
    log_info "  - /usr/local/bin/whmcs-db-monitor (performance monitoring)"
}

setup_cron_jobs() {
    log_info "Setting up automated maintenance cron jobs..."
    
    # Create cron jobs for database maintenance
    cat > /etc/cron.d/whmcs-db-maintenance <<EOF
# WHMCS Database Maintenance Cron Jobs

# Daily backup at 2 AM
0 2 * * * root /usr/local/bin/whmcs-db-backup >> /var/log/whmcs-backup.log 2>&1

# Weekly optimization on Sunday at 3 AM
0 3 * * 0 root /usr/local/bin/whmcs-db-optimize >> /var/log/whmcs-optimize.log 2>&1

# Daily performance report at 6 AM (optional - comment out if not needed)
# 0 6 * * * root /usr/local/bin/whmcs-db-monitor >> /var/log/whmcs-monitor.log 2>&1
EOF

    log_info "Cron jobs configured for automated maintenance"
}

# Main execution
case "$1" in
    "optimize")
        check_root
        optimize_database_config
        systemctl restart mariadb
        log_info "Database configuration optimized and MariaDB restarted"
        ;;
    "scripts")
        check_root
        create_maintenance_scripts
        setup_cron_jobs
        log_info "Maintenance scripts and cron jobs configured"
        ;;
    "all")
        check_root
        optimize_database_config
        create_maintenance_scripts
        setup_cron_jobs
        systemctl restart mariadb
        log_info "Complete database tuning applied"
        ;;
    *)
        echo "WHMCS Database Tuning Script"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  optimize - Optimize database configuration based on system resources"
        echo "  scripts  - Create maintenance scripts and cron jobs"
        echo "  all      - Apply all optimizations"
        echo ""
        echo "Example:"
        echo "  $0 all"
        exit 1
        ;;
esac
