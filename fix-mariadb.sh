#!/bin/bash

# MariaDB Fix Script for WHMCS
# This script fixes common MariaDB issues and ensures compatibility

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

check_mariadb_version() {
    log_info "Checking MariaDB version..."
    MARIADB_VERSION=$(mysql --version | grep -oP 'MariaDB \K[0-9]+\.[0-9]+')
    log_info "MariaDB version: $MARIADB_VERSION"
    
    # Check if version is 10.4 or higher (where mysql.user view changed)
    if [[ $(echo "$MARIADB_VERSION >= 10.4" | bc -l) -eq 1 ]]; then
        log_info "Modern MariaDB detected (>= 10.4)"
        return 0
    else
        log_info "Legacy MariaDB detected (< 10.4)"
        return 1
    fi
}

fix_root_access() {
    log_info "Fixing root access for MariaDB..."
    
    # Stop MariaDB
    systemctl stop mariadb
    
    # Start MariaDB in safe mode
    log_info "Starting MariaDB in safe mode..."
    mysqld_safe --skip-grant-tables --skip-networking &
    SAFE_PID=$!
    
    # Wait for MariaDB to start
    sleep 5
    
    # Fix root user
    log_info "Fixing root user authentication..."
    mysql -u root <<EOF
-- Flush privileges to enable changes
FLUSH PRIVILEGES;

-- Fix root user for modern MariaDB
ALTER USER 'root'@'localhost' IDENTIFIED VIA mysql_native_password USING PASSWORD('');

-- Ensure root has all privileges
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;

-- Flush privileges again
FLUSH PRIVILEGES;
EOF

    # Kill safe mode MariaDB
    kill $SAFE_PID
    sleep 2
    
    # Start MariaDB normally
    systemctl start mariadb
    
    log_info "Root access fixed successfully"
}

secure_mariadb_modern() {
    log_info "Securing MariaDB with modern syntax..."

    # Prepare log directories first
    log_info "Preparing MariaDB log directories..."
    mkdir -p /var/log/mysql
    chown mysql:mysql /var/log/mysql
    chmod 755 /var/log/mysql

    # Create binary log files if they don't exist
    touch /var/log/mysql/mysql-bin.index
    touch /var/log/mysql/mysql-bin.000001
    chown mysql:mysql /var/log/mysql/mysql-bin.* 2>/dev/null || true
    chmod 640 /var/log/mysql/mysql-bin.* 2>/dev/null || true

    # Generate root password
    ROOT_PASS=$(openssl rand -base64 32)

    mysql -u root <<EOF
-- Set root password using modern syntax
ALTER USER 'root'@'localhost' IDENTIFIED BY '${ROOT_PASS}';

-- Ensure root can access from all localhost variants
CREATE USER IF NOT EXISTS 'root'@'127.0.0.1' IDENTIFIED BY '${ROOT_PASS}';
CREATE USER IF NOT EXISTS 'root'@'::1' IDENTIFIED BY '${ROOT_PASS}';

-- Grant all privileges to root users
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'::1' WITH GRANT OPTION;

-- Remove anonymous users
DELETE FROM mysql.user WHERE User='';

-- Remove remote root login (keep only localhost variants)
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- Remove test database
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';

-- Reload privilege tables
FLUSH PRIVILEGES;
EOF

    # Save root password
    echo "MYSQL_ROOT_PASS=${ROOT_PASS}" > /root/mariadb-root-credentials.txt
    chmod 600 /root/mariadb-root-credentials.txt
    
    log_info "MariaDB secured successfully"
    log_info "Root password saved to /root/mariadb-root-credentials.txt"
}

create_whmcs_database() {
    log_info "Creating WHMCS database and user..."
    
    # Load credentials if they exist
    if [ -f "/root/mariadb-root-credentials.txt" ]; then
        source /root/mariadb-root-credentials.txt
    else
        log_error "Root credentials not found. Run 'secure' command first."
        exit 1
    fi
    
    # Get configuration
    DB_NAME="${1:-whmcs_$(date +%s)}"
    DB_USER="${2:-whmcs_user}"
    DB_PASS="${3:-$(openssl rand -base64 32)}"
    APP_HOST="${4:-********}"
    
    mysql -u root -p${MYSQL_ROOT_PASS} <<EOF
-- Create database
CREATE DATABASE IF NOT EXISTS ${DB_NAME} 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- Create users
CREATE USER IF NOT EXISTS '${DB_USER}'@'${APP_HOST}' IDENTIFIED BY '${DB_PASS}';
CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';
CREATE USER IF NOT EXISTS '${DB_USER}'@'%' IDENTIFIED BY '${DB_PASS}';

-- Grant privileges
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'${APP_HOST}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'%';

-- Reload privileges
FLUSH PRIVILEGES;

-- Show results
SHOW DATABASES LIKE '${DB_NAME}';
SELECT User, Host FROM mysql.user WHERE User='${DB_USER}';
EOF

    # Save WHMCS credentials
    cat > /root/whmcs-db-credentials.txt <<EOF
DB_HOST=localhost
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASS=${DB_PASS}
APP_HOST=${APP_HOST}
MYSQL_ROOT_PASS=${MYSQL_ROOT_PASS}
EOF

    chmod 600 /root/whmcs-db-credentials.txt
    
    log_info "WHMCS database created successfully"
    log_info "Database: ${DB_NAME}"
    log_info "User: ${DB_USER}"
    log_info "Credentials saved to /root/whmcs-db-credentials.txt"
}

test_connection() {
    log_info "Testing database connections..."
    
    if [ -f "/root/whmcs-db-credentials.txt" ]; then
        source /root/whmcs-db-credentials.txt
    else
        log_error "WHMCS credentials not found"
        exit 1
    fi
    
    # Test root connection
    log_info "Testing root connection..."
    if mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT 1;" >/dev/null 2>&1; then
        log_info "✓ Root connection successful"
    else
        log_error "✗ Root connection failed"
    fi
    
    # Test WHMCS user connection
    log_info "Testing WHMCS user connection..."
    if mysql -u ${DB_USER} -p${DB_PASS} -e "USE ${DB_NAME}; SELECT 1;" >/dev/null 2>&1; then
        log_info "✓ WHMCS user connection successful"
    else
        log_error "✗ WHMCS user connection failed"
    fi
    
    # Show database info
    log_info "Database information:"
    mysql -u root -p${MYSQL_ROOT_PASS} -e "
        SELECT 
            SCHEMA_NAME as 'Database',
            DEFAULT_CHARACTER_SET_NAME as 'Charset',
            DEFAULT_COLLATION_NAME as 'Collation'
        FROM information_schema.SCHEMATA 
        WHERE SCHEMA_NAME = '${DB_NAME}';
    "
}

fix_binlog_issues() {
    log_info "Fixing binary log issues..."

    # Stop MariaDB
    systemctl stop mariadb

    # Create log directory and files
    mkdir -p /var/log/mysql
    chown mysql:mysql /var/log/mysql
    chmod 755 /var/log/mysql

    # Create binary log files
    touch /var/log/mysql/mysql-bin.index
    touch /var/log/mysql/mysql-bin.000001
    touch /var/log/mysql/slow.log
    touch /var/log/mysql/error.log

    # Set proper ownership and permissions
    chown mysql:mysql /var/log/mysql/*
    chmod 640 /var/log/mysql/*

    # Initialize binary log index file
    echo "./mysql-bin.000001" > /var/log/mysql/mysql-bin.index

    # Start MariaDB
    systemctl start mariadb

    log_info "Binary log issues fixed"
}

show_status() {
    log_info "MariaDB Status Report"
    echo "===================="

    # Service status
    echo "Service Status:"
    systemctl status mariadb --no-pager -l | head -10
    echo

    # Version info
    echo "Version Information:"
    mysql --version
    echo

    # Log files status
    echo "Log Files Status:"
    ls -la /var/log/mysql/ 2>/dev/null || echo "Log directory not found"
    echo

    # Connection test
    echo "Connection Test:"
    if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
        echo "✓ Root connection (no password)"
    else
        echo "✗ Root connection requires password"
    fi

    # Show databases
    echo
    echo "Databases:"
    mysql -u root -e "SHOW DATABASES;" 2>/dev/null || echo "Cannot list databases (authentication required)"

    # Show users
    echo
    echo "Users:"
    mysql -u root -e "SELECT User, Host FROM mysql.user;" 2>/dev/null || echo "Cannot list users (authentication required)"

    # Check for recent errors
    echo
    echo "Recent Errors:"
    tail -5 /var/log/mysql/error.log 2>/dev/null || echo "No error log found"
}

show_help() {
    echo "MariaDB Fix Script for WHMCS"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  fix-root     - Fix root access issues"
    echo "  fix-binlog   - Fix binary log issues (mysql-bin.index errors)"
    echo "  secure       - Secure MariaDB installation"
    echo "  create-db    - Create WHMCS database and user"
    echo "  test         - Test database connections"
    echo "  status       - Show MariaDB status"
    echo ""
    echo "Examples:"
    echo "  $0 fix-root"
    echo "  $0 fix-binlog"
    echo "  $0 secure"
    echo "  $0 create-db [db_name] [db_user] [db_pass] [app_host]"
    echo "  $0 test"
    echo ""
    echo "Note: Run commands in order: fix-root → fix-binlog → secure → create-db → test"
}

# Main script
case "$1" in
    "fix-root")
        check_root
        fix_root_access
        ;;
    "fix-binlog")
        check_root
        fix_binlog_issues
        ;;
    "secure")
        check_root
        secure_mariadb_modern
        ;;
    "create-db")
        check_root
        create_whmcs_database "$2" "$3" "$4" "$5"
        ;;
    "test")
        test_connection
        ;;
    "status")
        show_status
        ;;
    *)
        show_help
        exit 1
        ;;
esac
