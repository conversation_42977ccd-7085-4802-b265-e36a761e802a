#!/bin/bash

# WHMCS Reverse Proxy Configuration Script
# This script configures Nginx as a reverse proxy for WHMCS
# Run this on your reverse proxy server (load balancer/edge server)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="yourdomain.com"
BACKEND_SERVERS=("********:80")  # Add multiple backend servers for load balancing
SSL_CERT_PATH="/etc/ssl/certs/whmcs.crt"
SSL_KEY_PATH="/etc/ssl/private/whmcs.key"
ENABLE_LOAD_BALANCING="false"
ENABLE_SSL="true"
ENABLE_RATE_LIMITING="true"
ENABLE_WAF="true"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

install_nginx() {
    log_info "Installing and configuring Nginx as reverse proxy..."
    
    # Install Nginx
    apt update
    apt install -y nginx nginx-extras
    
    # Enable and start Nginx
    systemctl enable nginx
    systemctl start nginx
}

configure_upstream() {
    log_info "Configuring upstream backend servers..."
    
    cat > /etc/nginx/conf.d/whmcs-upstream.conf <<EOF
# WHMCS Backend Servers
upstream whmcs_backend {
    # Load balancing method
    $([ "$ENABLE_LOAD_BALANCING" = "true" ] && echo "least_conn;" || echo "# Single server mode")
    
    # Backend servers
EOF

    for server in "${BACKEND_SERVERS[@]}"; do
        echo "    server $server max_fails=3 fail_timeout=30s;" >> /etc/nginx/conf.d/whmcs-upstream.conf
    done

    cat >> /etc/nginx/conf.d/whmcs-upstream.conf <<EOF
    
    # Health check
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}
EOF

    log_info "Upstream configuration created with ${#BACKEND_SERVERS[@]} backend server(s)"
}

configure_rate_limiting() {
    if [ "$ENABLE_RATE_LIMITING" = "true" ]; then
        log_info "Configuring rate limiting..."
        
        cat > /etc/nginx/conf.d/whmcs-rate-limit.conf <<EOF
# Rate limiting zones for WHMCS
limit_req_zone \$binary_remote_addr zone=whmcs_login:10m rate=5r/m;
limit_req_zone \$binary_remote_addr zone=whmcs_api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=whmcs_admin:10m rate=20r/m;
limit_req_zone \$binary_remote_addr zone=whmcs_general:10m rate=100r/m;

# Connection limiting
limit_conn_zone \$binary_remote_addr zone=whmcs_conn:10m;
EOF
    fi
}

configure_waf() {
    if [ "$ENABLE_WAF" = "true" ]; then
        log_info "Configuring basic WAF rules..."
        
        cat > /etc/nginx/conf.d/whmcs-waf.conf <<EOF
# Basic WAF rules for WHMCS
map \$request_uri \$blocked_request {
    default 0;
    "~*\.(php|asp|aspx|jsp)\$" 0;
    "~*\.(sql|bak|backup|old|tmp)\$" 1;
    "~*/\\..*" 1;
    "~*/(config|configuration|admin/config)" 1;
    "~*/wp-admin" 1;
    "~*/phpmyadmin" 1;
}

map \$http_user_agent \$blocked_agent {
    default 0;
    "~*bot" 0;
    "~*crawler" 0;
    "~*spider" 0;
    "~*scanner" 1;
    "~*nikto" 1;
    "~*sqlmap" 1;
    "" 1;
}

map \$query_string \$blocked_query {
    default 0;
    "~*union.*select" 1;
    "~*drop.*table" 1;
    "~*insert.*into" 1;
    "~*<script" 1;
    "~*javascript:" 1;
}
EOF
    fi
}

configure_ssl() {
    if [ "$ENABLE_SSL" = "true" ]; then
        log_info "Configuring SSL termination..."
        
        # Create SSL directory if it doesn't exist
        mkdir -p /etc/ssl/certs /etc/ssl/private
        
        # Generate self-signed certificate if none exists
        if [ ! -f "$SSL_CERT_PATH" ] || [ ! -f "$SSL_KEY_PATH" ]; then
            log_warn "SSL certificates not found, generating self-signed certificate..."
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout "$SSL_KEY_PATH" \
                -out "$SSL_CERT_PATH" \
                -subj "/C=ID/ST=Jakarta/L=Jakarta/O=WHMCS/CN=$DOMAIN"
            chmod 600 "$SSL_KEY_PATH"
            chmod 644 "$SSL_CERT_PATH"
        fi
        
        cat > /etc/nginx/conf.d/whmcs-ssl.conf <<EOF
# SSL Configuration for WHMCS
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
ssl_prefer_server_ciphers on;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
EOF
    fi
}

configure_main_site() {
    log_info "Configuring main WHMCS reverse proxy site..."
    
    cat > /etc/nginx/sites-available/whmcs-proxy <<EOF
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN};
    
    # Security headers even for HTTP
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Redirect to HTTPS
    $([ "$ENABLE_SSL" = "true" ] && echo "return 301 https://\$server_name\$request_uri;" || echo "# SSL disabled - serving HTTP directly")
    
    $([ "$ENABLE_SSL" = "false" ] && cat <<'HTTPEOF'
    # Rate limiting
    limit_req zone=whmcs_general burst=20 nodelay;
    limit_conn whmcs_conn 10;
    
    # WAF checks
    if ($blocked_request) { return 403; }
    if ($blocked_agent) { return 403; }
    if ($blocked_query) { return 403; }
    
    # Proxy to backend
    location / {
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
HTTPEOF
)
}

$([ "$ENABLE_SSL" = "true" ] && cat <<'SSLEOF'
# HTTPS server
server {
    listen 443 ssl http2;
    server_name DOMAIN_PLACEHOLDER www.DOMAIN_PLACEHOLDER;
    
    # SSL Configuration
    ssl_certificate SSL_CERT_PATH_PLACEHOLDER;
    ssl_certificate_key SSL_KEY_PATH_PLACEHOLDER;
    
    # Include SSL settings
    include /etc/nginx/conf.d/whmcs-ssl.conf;
    
    # Logging
    access_log /var/log/nginx/whmcs-proxy-access.log;
    error_log /var/log/nginx/whmcs-proxy-error.log;
    
    # Rate limiting with different zones for different areas
    location /login {
        limit_req zone=whmcs_login burst=3 nodelay;
        limit_conn whmcs_conn 5;
        
        # WAF checks
        if ($blocked_request) { return 403; }
        if ($blocked_agent) { return 403; }
        if ($blocked_query) { return 403; }
        
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    location /admin {
        limit_req zone=whmcs_admin burst=10 nodelay;
        limit_conn whmcs_conn 5;
        
        # WAF checks
        if ($blocked_request) { return 403; }
        if ($blocked_agent) { return 403; }
        if ($blocked_query) { return 403; }
        
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    location /api {
        limit_req zone=whmcs_api burst=20 nodelay;
        limit_conn whmcs_conn 10;
        
        # WAF checks
        if ($blocked_request) { return 403; }
        if ($blocked_agent) { return 403; }
        if ($blocked_query) { return 403; }
        
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Static files caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache static files at proxy level
        proxy_cache_valid 200 1d;
        proxy_cache_valid 404 1m;
        add_header X-Cache-Status $upstream_cache_status;
        
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Default location
    location / {
        limit_req zone=whmcs_general burst=20 nodelay;
        limit_conn whmcs_conn 10;
        
        # WAF checks
        if ($blocked_request) { return 403; }
        if ($blocked_agent) { return 403; }
        if ($blocked_query) { return 403; }
        
        proxy_pass http://whmcs_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Proxy timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
SSLEOF
)
EOF

    # Replace placeholders
    sed -i "s/DOMAIN_PLACEHOLDER/${DOMAIN}/g" /etc/nginx/sites-available/whmcs-proxy
    sed -i "s|SSL_CERT_PATH_PLACEHOLDER|${SSL_CERT_PATH}|g" /etc/nginx/sites-available/whmcs-proxy
    sed -i "s|SSL_KEY_PATH_PLACEHOLDER|${SSL_KEY_PATH}|g" /etc/nginx/sites-available/whmcs-proxy
    
    # Enable site
    ln -sf /etc/nginx/sites-available/whmcs-proxy /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
}

configure_monitoring() {
    log_info "Setting up monitoring and health checks..."
    
    # Create monitoring script
    cat > /usr/local/bin/whmcs-proxy-monitor <<'EOF'
#!/bin/bash
# WHMCS Reverse Proxy Monitor

echo "=== WHMCS Reverse Proxy Status ==="
echo "Generated: $(date)"
echo

# Check Nginx status
echo "=== Nginx Status ==="
systemctl status nginx --no-pager -l
echo

# Check upstream servers
echo "=== Backend Server Health ==="
for server in $(grep "server " /etc/nginx/conf.d/whmcs-upstream.conf | awk '{print $2}' | cut -d';' -f1); do
    if curl -s --connect-timeout 5 "http://$server/health" >/dev/null 2>&1; then
        echo "$server: UP"
    else
        echo "$server: DOWN"
    fi
done
echo

# Show connection statistics
echo "=== Connection Statistics ==="
ss -tuln | grep -E ":(80|443)"
echo

# Show recent errors
echo "=== Recent Errors ==="
tail -10 /var/log/nginx/whmcs-proxy-error.log 2>/dev/null || echo "No errors found"
EOF

    chmod +x /usr/local/bin/whmcs-proxy-monitor
    
    log_info "Monitoring script created: /usr/local/bin/whmcs-proxy-monitor"
}

# Main execution
case "$1" in
    "install")
        check_root
        install_nginx
        configure_upstream
        configure_rate_limiting
        configure_waf
        configure_ssl
        configure_main_site
        configure_monitoring
        
        # Test configuration
        nginx -t
        systemctl restart nginx
        
        log_info "WHMCS reverse proxy configuration completed!"
        log_info "Domain: $DOMAIN"
        log_info "Backend servers: ${BACKEND_SERVERS[*]}"
        log_info "SSL enabled: $ENABLE_SSL"
        log_info "Rate limiting enabled: $ENABLE_RATE_LIMITING"
        log_info "WAF enabled: $ENABLE_WAF"
        ;;
    "monitor")
        /usr/local/bin/whmcs-proxy-monitor
        ;;
    "test")
        nginx -t
        log_info "Configuration test completed"
        ;;
    *)
        echo "WHMCS Reverse Proxy Configuration Script"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  install  - Install and configure reverse proxy"
        echo "  monitor  - Show proxy status and backend health"
        echo "  test     - Test Nginx configuration"
        echo ""
        echo "Configuration (edit script variables):"
        echo "  DOMAIN: Your domain name"
        echo "  BACKEND_SERVERS: Array of backend server IPs"
        echo "  ENABLE_SSL: Enable SSL termination"
        echo "  ENABLE_RATE_LIMITING: Enable rate limiting"
        echo "  ENABLE_WAF: Enable basic WAF rules"
        echo ""
        echo "Example:"
        echo "  $0 install"
        exit 1
        ;;
esac
