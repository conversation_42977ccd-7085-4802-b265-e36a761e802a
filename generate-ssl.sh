#!/bin/bash

# SSL Certificate Generator for WHMCS
# This script generates self-signed SSL certificates for testing
# For production, use Let's Encrypt or commercial certificates

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="localhost"
SSL_DIR="/etc/ssl"
CERT_DIR="${SSL_DIR}/certs"
KEY_DIR="${SSL_DIR}/private"
CERT_FILE="${CERT_DIR}/whmcs.crt"
KEY_FILE="${KEY_DIR}/whmcs.key"
CSR_FILE="${SSL_DIR}/whmcs.csr"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

generate_self_signed() {
    log_info "Generating self-signed SSL certificate for ${DOMAIN}..."
    
    # Create directories if they don't exist
    mkdir -p ${CERT_DIR}
    mkdir -p ${KEY_DIR}
    
    # Generate private key
    log_info "Generating private key..."
    openssl genrsa -out ${KEY_FILE} 2048
    
    # Generate certificate
    log_info "Generating certificate..."
    openssl req -new -x509 -key ${KEY_FILE} -out ${CERT_FILE} -days 365 -subj "/C=ID/ST=Jakarta/L=Jakarta/O=WHMCS/OU=IT/CN=${DOMAIN}/emailAddress=admin@${DOMAIN}"
    
    # Set proper permissions
    chmod 600 ${KEY_FILE}
    chmod 644 ${CERT_FILE}
    
    log_info "Self-signed SSL certificate generated successfully!"
    log_info "Certificate: ${CERT_FILE}"
    log_info "Private Key: ${KEY_FILE}"
    log_warn "This is a self-signed certificate - browsers will show security warnings"
}

generate_csr() {
    log_info "Generating Certificate Signing Request (CSR) for ${DOMAIN}..."
    
    # Create directories if they don't exist
    mkdir -p ${CERT_DIR}
    mkdir -p ${KEY_DIR}
    
    # Generate private key
    log_info "Generating private key..."
    openssl genrsa -out ${KEY_FILE} 2048
    
    # Generate CSR
    log_info "Generating CSR..."
    openssl req -new -key ${KEY_FILE} -out ${CSR_FILE} -subj "/C=ID/ST=Jakarta/L=Jakarta/O=WHMCS/OU=IT/CN=${DOMAIN}/emailAddress=admin@${DOMAIN}"
    
    # Set proper permissions
    chmod 600 ${KEY_FILE}
    chmod 644 ${CSR_FILE}
    
    log_info "CSR generated successfully!"
    log_info "CSR File: ${CSR_FILE}"
    log_info "Private Key: ${KEY_FILE}"
    log_info "Submit the CSR to your Certificate Authority to get a signed certificate"
    
    # Display CSR content
    echo ""
    log_info "CSR Content (copy this to your CA):"
    echo "----------------------------------------"
    cat ${CSR_FILE}
    echo "----------------------------------------"
}

install_letsencrypt() {
    log_info "Installing Let's Encrypt certificate for ${DOMAIN}..."
    
    # Check if certbot is installed
    if ! command -v certbot &> /dev/null; then
        log_info "Installing Certbot..."
        apt update
        apt install -y certbot python3-certbot-nginx
    fi
    
    # Stop nginx temporarily
    systemctl stop nginx
    
    # Generate certificate
    log_info "Generating Let's Encrypt certificate..."
    certbot certonly --standalone -d ${DOMAIN} --non-interactive --agree-tos --email admin@${DOMAIN}
    
    # Create symlinks to standard locations
    ln -sf /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ${CERT_FILE}
    ln -sf /etc/letsencrypt/live/${DOMAIN}/privkey.pem ${KEY_FILE}
    
    # Start nginx
    systemctl start nginx
    
    # Setup auto-renewal
    log_info "Setting up auto-renewal..."
    cat > /etc/cron.d/letsencrypt-renewal <<EOF
# Let's Encrypt certificate renewal
0 2 * * * root certbot renew --quiet --post-hook "systemctl reload nginx"
EOF

    log_info "Let's Encrypt certificate installed successfully!"
    log_info "Certificate: ${CERT_FILE} -> /etc/letsencrypt/live/${DOMAIN}/fullchain.pem"
    log_info "Private Key: ${KEY_FILE} -> /etc/letsencrypt/live/${DOMAIN}/privkey.pem"
    log_info "Auto-renewal configured"
}

show_certificate_info() {
    if [ -f "${CERT_FILE}" ]; then
        log_info "Certificate Information:"
        echo "----------------------------------------"
        openssl x509 -in ${CERT_FILE} -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:)"
        echo "----------------------------------------"
        
        # Check certificate expiry
        expiry_date=$(openssl x509 -in ${CERT_FILE} -noout -enddate | cut -d= -f2)
        expiry_epoch=$(date -d "${expiry_date}" +%s)
        current_epoch=$(date +%s)
        days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [ ${days_until_expiry} -lt 30 ]; then
            log_warn "Certificate expires in ${days_until_expiry} days!"
        else
            log_info "Certificate expires in ${days_until_expiry} days"
        fi
    else
        log_error "Certificate file not found: ${CERT_FILE}"
    fi
}

test_ssl() {
    log_info "Testing SSL configuration..."
    
    if [ -f "${CERT_FILE}" ] && [ -f "${KEY_FILE}" ]; then
        # Test certificate and key match
        cert_hash=$(openssl x509 -noout -modulus -in ${CERT_FILE} | openssl md5)
        key_hash=$(openssl rsa -noout -modulus -in ${KEY_FILE} | openssl md5)
        
        if [ "${cert_hash}" = "${key_hash}" ]; then
            log_info "Certificate and private key match ✓"
        else
            log_error "Certificate and private key do not match ✗"
            exit 1
        fi
        
        # Test Nginx configuration
        if nginx -t 2>/dev/null; then
            log_info "Nginx SSL configuration is valid ✓"
        else
            log_error "Nginx SSL configuration has errors ✗"
            nginx -t
        fi
        
        # Test SSL connection (if nginx is running)
        if systemctl is-active --quiet nginx; then
            if echo | openssl s_client -connect localhost:443 -servername ${DOMAIN} 2>/dev/null | grep -q "Verify return code: 0"; then
                log_info "SSL connection test successful ✓"
            else
                log_warn "SSL connection test failed (this is normal for self-signed certificates)"
            fi
        fi
    else
        log_error "Certificate or key file missing"
        exit 1
    fi
}

show_help() {
    echo "SSL Certificate Generator for WHMCS"
    echo ""
    echo "Usage: $0 [COMMAND] [DOMAIN]"
    echo ""
    echo "Commands:"
    echo "  self-signed  - Generate self-signed certificate (for testing)"
    echo "  csr          - Generate Certificate Signing Request"
    echo "  letsencrypt  - Install Let's Encrypt certificate (requires public domain)"
    echo "  info         - Show certificate information"
    echo "  test         - Test SSL configuration"
    echo ""
    echo "Examples:"
    echo "  $0 self-signed yourdomain.com"
    echo "  $0 letsencrypt yourdomain.com"
    echo "  $0 info"
    echo "  $0 test"
    echo ""
    echo "Note: For production use, always use certificates from trusted CAs"
}

# Parse arguments
if [ $# -ge 2 ]; then
    DOMAIN="$2"
fi

# Main script
case "$1" in
    "self-signed")
        check_root
        generate_self_signed
        test_ssl
        ;;
    "csr")
        check_root
        generate_csr
        ;;
    "letsencrypt")
        check_root
        if [ "${DOMAIN}" = "localhost" ]; then
            log_error "Let's Encrypt requires a public domain name, not localhost"
            exit 1
        fi
        install_letsencrypt
        test_ssl
        ;;
    "info")
        show_certificate_info
        ;;
    "test")
        test_ssl
        ;;
    *)
        show_help
        exit 1
        ;;
esac
