#!/bin/bash

# WHMCS Installation Script for Debian/Ubuntu
# Usage: ./whmcs-setup.sh db|app
# Supports separate database and application servers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="localhost"
WEBROOT="/var/www/whmcs"
DB_NAME="whmcs_$(date +%s)"
DB_USER="whmcs_user"
DB_PASS=$(openssl rand -base64 32)
DB_HOST="********"  # Change this to your database server IP
APP_HOST="********"  # Change this to your app server IP
PROXY_HOST="********"  # Change this to your reverse proxy server IP
PHP_VERSION="8.1"    # WHMCS recommended PHP version
BEHIND_PROXY="true"   # Set to "false" if not behind reverse proxy

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

update_system() {
    log_info "Updating system packages..."
    apt update && apt upgrade -y
    
    # Install common tools
    apt install -y curl wget unzip git htop iotop net-tools software-properties-common
}

install_database() {
    log_info "Installing and configuring MariaDB for WHMCS..."
    
    # Install MariaDB
    apt install -y mariadb-server mariadb-client
    
    # Configure MariaDB for remote connections
    log_info "Configuring MariaDB for remote connections..."
    sed -i 's/bind-address.*/bind-address = 0.0.0.0/' /etc/mysql/mariadb.conf.d/50-server.cnf
    
    # Start and enable MariaDB
    systemctl start mariadb
    systemctl enable mariadb
    
    # Secure MariaDB installation (automated)
    log_info "Securing MariaDB installation..."
    mysql -u root <<EOF
UPDATE mysql.user SET Password=PASSWORD('$(openssl rand -base64 32)') WHERE User='root';
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
DROP DATABASE IF EXISTS test;
DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
FLUSH PRIVILEGES;
EOF

    # Create database and user for WHMCS
    log_info "Creating WHMCS database and user..."
    mysql -u root <<EOF
CREATE DATABASE ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER '${DB_USER}'@'${APP_HOST}' IDENTIFIED BY '${DB_PASS}';
CREATE USER '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'${APP_HOST}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';
FLUSH PRIVILEGES;
EOF

    # Optimize MariaDB configuration for WHMCS
    log_info "Optimizing MariaDB configuration for WHMCS..."
    cat > /etc/mysql/mariadb.conf.d/99-whmcs-optimizations.cnf <<EOF
[mysqld]
# Basic settings
max_connections = 300
thread_cache_size = 100
table_open_cache = 8000
table_definition_cache = 4000

# InnoDB settings for better performance
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 512M
innodb_log_buffer_size = 32M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_io_capacity = 1000
innodb_io_capacity_max = 2000

# Query cache (for read-heavy workloads like WHMCS)
query_cache_type = 1
query_cache_size = 512M
query_cache_limit = 4M

# Temporary tables
tmp_table_size = 256M
max_heap_table_size = 256M

# MyISAM settings (some WHMCS modules may use MyISAM)
key_buffer_size = 256M
myisam_sort_buffer_size = 128M

# Binary logging for replication/backup
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Slow query log for optimization
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1

# Character set for WHMCS
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# Security settings
local_infile = 0
skip_show_database

# Network settings
max_allowed_packet = 64M
connect_timeout = 10
wait_timeout = 600
interactive_timeout = 600

# Sort and group settings
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 4M
EOF

    # Configure firewall for database server
    log_info "Configuring firewall for database server..."
    ufw allow from ${APP_HOST} to any port 3306
    ufw --force enable
    
    # Restart MariaDB
    systemctl restart mariadb
    
    # Save database credentials
    cat > /root/whmcs-db-credentials.txt <<EOF
DB_HOST=${DB_HOST}
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASS=${DB_PASS}
APP_HOST=${APP_HOST}
EOF
    
    chmod 600 /root/whmcs-db-credentials.txt
    
    log_info "Database configuration saved to /root/whmcs-db-credentials.txt"
    log_info "Database setup completed successfully!"
    log_info "Make sure to copy the credentials file to your app server"
}

install_app() {
    log_info "Installing WHMCS application server..."
    
    # Check if credentials file exists
    if [[ ! -f "/root/whmcs-db-credentials.txt" ]]; then
        log_error "Database credentials file not found."
        log_error "Please copy /root/whmcs-db-credentials.txt from your database server"
        log_error "Or run './whmcs-setup.sh db' first if this is a single server setup"
        exit 1
    fi
    
    # Load database credentials
    source /root/whmcs-db-credentials.txt
    
    # Add PHP repository for latest versions
    add-apt-repository -y ppa:ondrej/php
    apt update
    
    # Install required packages for WHMCS
    log_info "Installing required packages for WHMCS..."
    apt install -y nginx php${PHP_VERSION}-fpm php${PHP_VERSION}-mysql php${PHP_VERSION}-xml \
        php${PHP_VERSION}-gd php${PHP_VERSION}-curl php${PHP_VERSION}-zip php${PHP_VERSION}-mbstring \
        php${PHP_VERSION}-intl php${PHP_VERSION}-bcmath php${PHP_VERSION}-opcache php${PHP_VERSION}-cli \
        php${PHP_VERSION}-common php${PHP_VERSION}-soap php${PHP_VERSION}-imap php${PHP_VERSION}-json \
        php${PHP_VERSION}-readline php${PHP_VERSION}-simplexml php${PHP_VERSION}-tokenizer \
        php${PHP_VERSION}-xmlwriter php${PHP_VERSION}-dom redis-server memcached \
        php${PHP_VERSION}-redis php${PHP_VERSION}-memcached
    
    # Configure Redis for session storage and caching
    log_info "Configuring Redis for WHMCS..."
    cat > /etc/redis/redis.conf <<EOF
# Redis configuration for WHMCS
bind 127.0.0.1
port 6379
timeout 300
keepalive 60
tcp-backlog 511
databases 16

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security
requirepass $(openssl rand -base64 32)
EOF

    systemctl restart redis-server
    systemctl enable redis-server
    
    # Configure Memcached
    log_info "Configuring Memcached..."
    sed -i 's/-m 64/-m 256/' /etc/memcached.conf
    systemctl restart memcached
    systemctl enable memcached
    
    # Configure PHP-FPM for WHMCS
    log_info "Configuring PHP-FPM for WHMCS..."
    cat > /etc/php/${PHP_VERSION}/fpm/pool.d/whmcs.conf <<EOF
[whmcs]
user = www-data
group = www-data
listen = /run/php/php${PHP_VERSION}-fpm-whmcs.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

# Process management
pm = dynamic
pm.max_children = 100
pm.start_servers = 20
pm.min_spare_servers = 10
pm.max_spare_servers = 30
pm.max_requests = 1000
pm.process_idle_timeout = 60s

# PHP settings optimized for WHMCS
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M
php_admin_value[max_input_vars] = 3000
php_admin_value[session.gc_maxlifetime] = 7200

# Error logging
php_admin_value[log_errors] = on
php_admin_value[error_log] = /var/log/php/whmcs-error.log
EOF

    # Create PHP log directory
    mkdir -p /var/log/php
    chown www-data:www-data /var/log/php
    
    # Configure PHP OPcache for maximum performance
    log_info "Configuring PHP OPcache for WHMCS..."
    cat > /etc/php/${PHP_VERSION}/mods-available/opcache-whmcs.ini <<EOF
; OPcache optimizations for WHMCS
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=512
opcache.interned_strings_buffer=128
opcache.max_accelerated_files=20000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
opcache.validate_timestamps=1
opcache.save_comments=1
opcache.enable_file_override=1
opcache.optimization_level=0x7FFFBFFF
opcache.preload_user=www-data
opcache.jit_buffer_size=256M
opcache.jit=1255
EOF

    # Enable OPcache configuration
    ln -sf /etc/php/${PHP_VERSION}/mods-available/opcache-whmcs.ini /etc/php/${PHP_VERSION}/fpm/conf.d/20-opcache-whmcs.ini
    ln -sf /etc/php/${PHP_VERSION}/mods-available/opcache-whmcs.ini /etc/php/${PHP_VERSION}/cli/conf.d/20-opcache-whmcs.ini
    
    # Create web directory
    mkdir -p ${WEBROOT}
    
    # Set proper permissions for WHMCS
    chown -R www-data:www-data ${WEBROOT}
    chmod -R 755 ${WEBROOT}
    
    log_info "Web directory created at ${WEBROOT}"
    log_info "Please upload your WHMCS files to this directory manually"
    
    # Configure Nginx for WHMCS (optimized for reverse proxy)
    log_info "Configuring Nginx for WHMCS behind reverse proxy..."

    if [ "$BEHIND_PROXY" = "true" ]; then
        # Configuration for behind reverse proxy
        cat > /etc/nginx/sites-available/whmcs <<EOF
# WHMCS server behind reverse proxy
server {
    listen 80;
    server_name ${DOMAIN} www.${DOMAIN} _;
    root ${WEBROOT};
    index index.php index.html index.htm;

    # Access and error logs with real IP logging
    access_log /var/log/nginx/whmcs-access.log main;
    error_log /var/log/nginx/whmcs-error.log;

    # Real IP configuration for reverse proxy
    real_ip_header X-Forwarded-For;
    real_ip_recursive on;
    set_real_ip_from ${PROXY_HOST};
    set_real_ip_from 10.0.0.0/8;
    set_real_ip_from **********/12;
    set_real_ip_from ***********/16;

    # Trust proxy headers
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;

    # Security headers (let reverse proxy handle HSTS)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Character set
    charset utf-8;

    # Client settings for large uploads
    client_max_body_size 100M;
    client_body_timeout 60;
    client_header_timeout 60;
    client_body_buffer_size 128k;

    # Handle forwarded protocol properly
    map \$http_x_forwarded_proto \$forwarded_proto {
        default \$scheme;
        https https;
        http http;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # WHMCS Admin area rewrite rules
    location ~ ^/admin/(client!\.php|client/(.*)|table/(.*)|search!\.php|search/(.*)|apps|billing|setup|user|services|addons|domains|utilities|emailmarketer!\.php|utilities/(.*)|logs|help!\.php|help/license|modules|image/(recent|upload)|validation_com/(.*))/?(.*)$ {
        rewrite ^/(.*)$ /admin/index.php?rp=/admin/\$1/\$2;
    }

    # WHMCS Client area rewrite rules
    location ~ ^/(images/em|invoice|login|password|account|store|download|knowledgebase|announcements|clientarea/ssl-certificates|user/(verification|accounts|profile|password|security|verify)|cart/(domain/renew)|domain/pricing|cart/order|images/kb)/?(.*)$ {
        rewrite ^/(.*)$ /index.php?rp=/\$1/\$2;
    }

    # Main location block
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # Favicon and robots.txt
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    # Error page
    error_page 404 /index.php;

    # PHP handling with optimizations and proxy support
    location ~ \.php$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/run/php/php${PHP_VERSION}-fpm-whmcs.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;

        # Pass proxy information to PHP
        fastcgi_param HTTP_X_FORWARDED_PROTO \$forwarded_proto;
        fastcgi_param HTTP_X_FORWARDED_FOR \$proxy_add_x_forwarded_for;
        fastcgi_param HTTP_X_REAL_IP \$remote_addr;
        fastcgi_param HTTPS \$https if_not_empty;

        # Set HTTPS param when behind SSL proxy
        set \$https_param "";
        if (\$http_x_forwarded_proto = "https") {
            set \$https_param "on";
        }
        fastcgi_param HTTPS \$https_param;

        # FastCGI optimizations
        fastcgi_buffers 32 32k;
        fastcgi_buffer_size 64k;
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_busy_buffers_size 64k;
        fastcgi_temp_file_write_size 64k;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|woff|woff2|ttf|eot)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
    }

    location ~* \.(css|js)$ {
        expires 7d;
        access_log off;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }

    # Block access to sensitive files and directories
    location ~ /\.(?!well-known).* {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block access to vendor directory
    location ^~ /vendor/ {
        deny all;
        return 403;
    }

    # Block access to configuration files
    location ~* \.(conf|cnf|sql|sh|bak)$ {
        deny all;
        return 403;
    }

    # Block access to .htaccess files
    location ~ /\.ht {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to PHP files in uploads directory
    location ~* /uploads/.*\.php$ {
        deny all;
        return 403;
    }
}
EOF
    else
        # Configuration for direct access (not behind proxy)
        cat > /etc/nginx/sites-available/whmcs <<EOF
# HTTP redirect to HTTPS
server {
    listen 80 default_server;
    server_name ${DOMAIN} www.${DOMAIN};
    return 301 https://${DOMAIN}\$request_uri;
}

# HTTPS server for WHMCS (direct access)
server {
    listen 443 ssl http2;
    server_name ${DOMAIN};
    root ${WEBROOT};
    index index.php index.html index.htm;

    # Access and error logs
    access_log /var/log/nginx/whmcs-access.log;
    error_log /var/log/nginx/whmcs-error.log;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/whmcs.crt;
    ssl_certificate_key /etc/ssl/private/whmcs.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Character set
    charset utf-8;

    # Client settings for large uploads
    client_max_body_size 100M;
    client_body_timeout 60;
    client_header_timeout 60;
    client_body_buffer_size 128k;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # WHMCS Admin area rewrite rules
    location ~ ^/admin/(client!\.php|client/(.*)|table/(.*)|search!\.php|search/(.*)|apps|billing|setup|user|services|addons|domains|utilities|emailmarketer!\.php|utilities/(.*)|logs|help!\.php|help/license|modules|image/(recent|upload)|validation_com/(.*))/?(.*)$ {
        rewrite ^/(.*)$ /admin/index.php?rp=/admin/\$1/\$2;
    }

    # WHMCS Client area rewrite rules
    location ~ ^/(images/em|invoice|login|password|account|store|download|knowledgebase|announcements|clientarea/ssl-certificates|user/(verification|accounts|profile|password|security|verify)|cart/(domain/renew)|domain/pricing|cart/order|images/kb)/?(.*)$ {
        rewrite ^/(.*)$ /index.php?rp=/\$1/\$2;
    }

    # Main location block
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # Favicon and robots.txt
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    location = /robots.txt {
        access_log off;
        log_not_found off;
    }

    # Error page
    error_page 404 /index.php;

    # PHP handling with optimizations
    location ~ \.php$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/run/php/php${PHP_VERSION}-fpm-whmcs.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$realpath_root\$fastcgi_script_name;
        include fastcgi_params;

        # FastCGI optimizations
        fastcgi_buffers 32 32k;
        fastcgi_buffer_size 64k;
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_busy_buffers_size 64k;
        fastcgi_temp_file_write_size 64k;
    }

    # Cache static files
    location ~* \.(jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|woff|woff2|ttf|eot)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
    }

    location ~* \.(css|js)$ {
        expires 7d;
        access_log off;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }

    # Block access to sensitive files and directories
    location ~ /\.(?!well-known).* {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block access to vendor directory
    location ^~ /vendor/ {
        deny all;
        return 403;
    }

    # Block access to configuration files
    location ~* \.(conf|cnf|sql|sh|bak)$ {
        deny all;
        return 403;
    }

    # Block access to .htaccess files
    location ~ /\.ht {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to PHP files in uploads directory
    location ~* /uploads/.*\.php$ {
        deny all;
        return 403;
    }
}
EOF
    fi

    # Enable site and disable default
    ln -sf /etc/nginx/sites-available/whmcs /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default

    # Test Nginx configuration
    nginx -t

    # Configure system optimizations for high performance
    log_info "Applying system optimizations for WHMCS..."

    # Kernel parameters for network performance
    cat >> /etc/sysctl.conf <<EOF

# WHMCS Performance Optimizations
# Network optimizations
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0

# Memory management
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.vfs_cache_pressure = 50

# File system optimizations
fs.file-max = 2097152
fs.inotify.max_user_watches = 524288
EOF

    sysctl -p

    # Configure limits for www-data user
    cat >> /etc/security/limits.conf <<EOF
www-data soft nofile 65536
www-data hard nofile 65536
www-data soft nproc 32768
www-data hard nproc 32768
EOF

    # Configure logrotate for WHMCS logs
    cat > /etc/logrotate.d/whmcs <<EOF
/var/log/nginx/whmcs*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 0644 www-data adm
    sharedscripts
    postrotate
        systemctl reload nginx
    endscript
}

/var/log/php/whmcs*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    sharedscripts
    postrotate
        systemctl reload php${PHP_VERSION}-fpm
    endscript
}
EOF

    # Configure firewall for app server
    log_info "Configuring firewall for application server..."
    ufw allow 22/tcp

    if [ "$BEHIND_PROXY" = "true" ]; then
        # Behind reverse proxy - only allow HTTP from proxy server
        ufw allow from ${PROXY_HOST} to any port 80
        log_info "Firewall configured for reverse proxy setup"
        log_info "Only allowing HTTP traffic from proxy server: ${PROXY_HOST}"
    else
        # Direct access - allow HTTP and HTTPS from anywhere
        ufw allow 80/tcp
        ufw allow 443/tcp
        log_info "Firewall configured for direct access"
    fi

    ufw --force enable

    # Restart services
    systemctl restart php${PHP_VERSION}-fpm
    systemctl restart nginx
    systemctl enable php${PHP_VERSION}-fpm
    systemctl enable nginx

    # Create installation info
    cat > /root/whmcs-installation-info.txt <<EOF
WHMCS Application Server Installation Complete!

Access your WHMCS installation at: $([ "$BEHIND_PROXY" = "true" ] && echo "http://${DOMAIN} (via reverse proxy)" || echo "https://${DOMAIN}")

Database Information:
Host: ${DB_HOST}
Name: ${DB_NAME}
User: ${DB_USER}
Password: ${DB_PASS}

Web Directory: ${WEBROOT}
PHP Version: ${PHP_VERSION}

Next Steps:
1. Upload your WHMCS files to ${WEBROOT}
2. Set proper file permissions: chown -R www-data:www-data ${WEBROOT}
$([ "$BEHIND_PROXY" = "true" ] && echo "3. Configure your reverse proxy to forward requests to this server" || echo "3. Configure SSL certificates in /etc/ssl/")
$([ "$BEHIND_PROXY" = "true" ] && echo "4. Visit your domain via reverse proxy to complete WHMCS setup" || echo "4. Visit https://${DOMAIN}/install.php to complete WHMCS setup")
5. Configure automated backups
6. Set up monitoring and alerting

Reverse Proxy Configuration:
$([ "$BEHIND_PROXY" = "true" ] && echo "- This server is configured to run behind a reverse proxy" || echo "- This server is configured for direct access")
$([ "$BEHIND_PROXY" = "true" ] && echo "- Proxy server IP: ${PROXY_HOST}" || echo "- SSL termination handled locally")
$([ "$BEHIND_PROXY" = "true" ] && echo "- Real IP detection configured for proper logging" || echo "- Direct SSL/TLS configuration required")

Performance Features Enabled:
- PHP ${PHP_VERSION} with OPcache and JIT
- Redis for session storage and caching
- Memcached for additional caching
- Nginx with HTTP/2 and compression
- MariaDB optimizations (on database server)
- Kernel network optimizations
- Static file caching with long expiry
- FastCGI optimizations

Security Features:
- Security headers configured
- Sensitive directories blocked
- File upload restrictions
- Firewall configured
- SSL/TLS hardening

Monitoring Commands:
- Check PHP-FPM status: systemctl status php${PHP_VERSION}-fpm
- Check Nginx status: systemctl status nginx
- Check Redis status: systemctl status redis-server
- Monitor PHP errors: tail -f /var/log/php/whmcs-error.log
- Monitor Nginx access: tail -f /var/log/nginx/whmcs-access.log
EOF

    chmod 600 /root/whmcs-installation-info.txt

    log_info "Application server setup completed successfully!"
    log_info "Installation details saved to /root/whmcs-installation-info.txt"
    log_info "Please upload your WHMCS files to ${WEBROOT} and configure SSL certificates"
}

show_help() {
    echo "WHMCS Installation Script for Separate Database and Application Servers"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  db   - Install and configure MariaDB database server"
    echo "  app  - Install WHMCS application server with Nginx and PHP"
    echo ""
    echo "Configuration:"
    echo "  Edit the script to set your server IPs:"
    echo "  - DB_HOST: Database server IP (default: ********)"
    echo "  - APP_HOST: Application server IP (default: ********)"
    echo "  - PROXY_HOST: Reverse proxy server IP (default: ********)"
    echo "  - DOMAIN: Your domain name (default: localhost)"
    echo "  - BEHIND_PROXY: Set to 'true' if behind reverse proxy (default: true)"
    echo ""
    echo "Example Installation Process:"
    echo "  1. On database server: $0 db"
    echo "  2. Copy /root/whmcs-db-credentials.txt to app server"
    echo "  3. On app server: $0 app"
    echo "  4. Upload WHMCS files to /var/www/whmcs"
    echo "  5. Configure SSL certificates"
    echo "  6. Complete WHMCS installation via web interface"
}

# Main script
case "$1" in
    "db")
        check_root
        update_system
        install_database
        ;;
    "app")
        check_root
        update_system
        install_app
        ;;
    *)
        show_help
        exit 1
        ;;
esac
