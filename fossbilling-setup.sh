#!/bin/bash

# FOSSBilling Installation Script for Debian
# Usage: ./fossbilling-setup.sh db|app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FOSSBILLING_VERSION="1.0.0"
DOMAIN="localhost"
WEBROOT="/var/www/fossbilling"
DB_NAME="fossbilling_$(date +%s)"
DB_USER="fossbilling_user"
DB_PASS=$(openssl rand -base64 32)
DB_HOST="********"
PHP_VERSION="8.2"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

update_system() {
    log_info "Updating system packages..."
    apt update && apt upgrade -y
}

install_database() {
    log_info "Installing and configuring MariaDB..."
    
    # Install MariaDB
    apt install -y mariadb-server mariadb-client
    
    # Secure MariaDB installation
    mysql_secure_installation
    
    # Start and enable MariaDB
    systemctl start mariadb
    systemctl enable mariadb
    
    # Create database and user
    log_info "Creating database and user..."
    mysql -u root <<EOF
CREATE DATABASE ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER '${DB_USER}'@'${DB_HOST}' IDENTIFIED BY '${DB_PASS}';
GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'${DB_HOST}';
FLUSH PRIVILEGES;
EOF

    # Optimize MariaDB configuration
    log_info "Optimizing MariaDB configuration..."
    cat > /etc/mysql/mariadb.conf.d/99-fossbilling.cnf <<EOF
[mysqld]
# Performance optimizations
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1

# Query cache
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# Connection settings
max_connections = 200
thread_cache_size = 50
table_open_cache = 4000
table_definition_cache = 2000

# Binary logging
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7

# Slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Character set
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
EOF

    # Restart MariaDB
    systemctl restart mariadb
    
    # Save database credentials
    cat > db.txt <<EOF
DB_HOST=${DB_HOST}
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASS=${DB_PASS}
EOF
    
    log_info "Database configuration saved to db.txt"
    log_info "Database setup completed successfully!"
}

install_app() {
    log_info "Installing FOSSBilling application..."
    
    # Check if db.txt exists
    if [[ ! -f "db.txt" ]]; then
        log_error "db.txt not found. Please run './fossbilling-setup.sh db' first"
        exit 1
    fi
    
    # Load database credentials
    source db.txt
    
    # Install required packages
    log_info "Installing required packages..."
    apt install -y nginx php${PHP_VERSION}-fpm php${PHP_VERSION}-mysql php${PHP_VERSION}-xml php${PHP_VERSION}-gd \
        php${PHP_VERSION}-curl php${PHP_VERSION}-zip php${PHP_VERSION}-mbstring php${PHP_VERSION}-intl \
        php${PHP_VERSION}-bcmath php${PHP_VERSION}-opcache php${PHP_VERSION}-cli php${PHP_VERSION}-common \
        curl wget unzip git
    
    # Configure PHP-FPM
    log_info "Configuring PHP-FPM..."
    cat > /etc/php/${PHP_VERSION}/fpm/pool.d/fossbilling.conf <<EOF
[fossbilling]
user = www-data
group = www-data
listen = /run/php/php${PHP_VERSION}-fpm-fossbilling.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 1000

php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 300
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M
EOF

    # Configure PHP OPcache
    log_info "Configuring PHP OPcache..."
    cat > /etc/php/${PHP_VERSION}/mods-available/opcache-fossbilling.ini <<EOF
; OPcache optimizations for FOSSBilling
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=64
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
opcache.validate_timestamps=1
opcache.save_comments=1
opcache.enable_file_override=1
opcache.optimization_level=0x7FFFBFFF
opcache.blacklist_filename=/etc/php/${PHP_VERSION}/opcache-blacklist.txt
EOF

    # Create OPcache blacklist
    touch /etc/php/${PHP_VERSION}/opcache-blacklist.txt
    
    # Enable OPcache configuration
    ln -sf /etc/php/${PHP_VERSION}/mods-available/opcache-fossbilling.ini /etc/php/${PHP_VERSION}/fpm/conf.d/20-opcache-fossbilling.ini
    ln -sf /etc/php/${PHP_VERSION}/mods-available/opcache-fossbilling.ini /etc/php/${PHP_VERSION}/cli/conf.d/20-opcache-fossbilling.ini
    
    # Create web directory
    mkdir -p ${WEBROOT}
    cd ${WEBROOT}
    
    # Download FOSSBilling
    log_info "Downloading FOSSBilling..."
    wget -O fossbilling.zip "https://github.com/FOSSBilling/FOSSBilling/releases/download/${FOSSBILLING_VERSION}/FOSSBilling.zip"
    unzip fossbilling.zip
    rm fossbilling.zip
    
    # Set permissions
    chown -R www-data:www-data ${WEBROOT}
    chmod -R 755 ${WEBROOT}
    chmod -R 775 ${WEBROOT}/data
    
    # Configure Nginx
    log_info "Configuring Nginx..."
    cat > /etc/nginx/sites-available/fossbilling <<EOF
server {
    listen 80;
    server_name ${DOMAIN};
    root ${WEBROOT};
    index index.php index.html;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Client settings
    client_max_body_size 100M;
    client_body_timeout 60;
    client_header_timeout 60;
    
    # Cache static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(data|config|logs)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # PHP handling
    location ~ \.php$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/run/php/php${PHP_VERSION}-fpm-fossbilling.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
        
        # FastCGI optimizations
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }
    
    # Pretty URLs
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    # Deny access to PHP files in uploads directory
    location ~* /uploads/.*\.php$ {
        deny all;
    }
}
EOF

    # Enable site
    ln -sf /etc/nginx/sites-available/fossbilling /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # Test Nginx configuration
    nginx -t
    
    # Configure system optimizations
    log_info "Applying system optimizations..."
    
    # Kernel parameters
    cat >> /etc/sysctl.conf <<EOF

# FOSSBilling optimizations
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_window_scaling = 1
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_sack = 1
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

    sysctl -p
    
    # Configure logrotate
    cat > /etc/logrotate.d/fossbilling <<EOF
/var/log/nginx/fossbilling*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 0644 www-data adm
    sharedscripts
    postrotate
        systemctl reload nginx
    endscript
}
EOF

    # Restart services
    systemctl restart php${PHP_VERSION}-fpm
    systemctl restart nginx
    systemctl enable php${PHP_VERSION}-fpm
    systemctl enable nginx
    
    # Create installation info
    cat > installation_info.txt <<EOF
FOSSBilling Installation Complete!

Access your installation at: http://${DOMAIN}

Database Information:
Host: ${DB_HOST}
Name: ${DB_NAME}
User: ${DB_USER}
Password: ${DB_PASS}

Web Directory: ${WEBROOT}
PHP Version: ${PHP_VERSION}

Next Steps:
1. Visit http://${DOMAIN}/install.php to complete the setup
2. Configure SSL certificate (recommended)
3. Set up automated backups
4. Configure monitoring

Performance Features Enabled:
- PHP OPcache
- Nginx compression
- Static file caching
- MariaDB optimizations
- Kernel network optimizations
EOF

    log_info "Application setup completed successfully!"
    log_info "Installation details saved to installation_info.txt"
}

show_help() {
    echo "FOSSBilling Installation Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  db   - Install and configure MariaDB database"
    echo "  app  - Install FOSSBilling application with Nginx"
    echo ""
    echo "Example:"
    echo "  $0 db"
    echo "  $0 app"
}

# Main script
case "$1" in
    "db")
        check_root
        update_system
        install_database
        ;;
    "app")
        check_root
        update_system
        install_app
        ;;
    *)
        show_help
        exit 1
        ;;
esac
