#!/bin/bash

# Test script for MariaDB fixes
# This script tests the fixes for common MariaDB issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

test_mariadb_service() {
    log_info "Testing MariaDB service status..."
    
    if systemctl is-active --quiet mariadb; then
        log_success "✓ MariaDB service is running"
        return 0
    else
        log_error "✗ MariaDB service is not running"
        return 1
    fi
}

test_log_directories() {
    log_info "Testing log directories and files..."
    
    local errors=0
    
    # Check log directory
    if [ -d "/var/log/mysql" ]; then
        log_success "✓ Log directory exists: /var/log/mysql"
    else
        log_error "✗ Log directory missing: /var/log/mysql"
        ((errors++))
    fi
    
    # Check binary log index file
    if [ -f "/var/log/mysql/mysql-bin.index" ]; then
        log_success "✓ Binary log index file exists"
    else
        log_error "✗ Binary log index file missing"
        ((errors++))
    fi
    
    # Check permissions
    local owner=$(stat -c '%U:%G' /var/log/mysql 2>/dev/null || echo "unknown")
    if [ "$owner" = "mysql:mysql" ]; then
        log_success "✓ Log directory has correct ownership (mysql:mysql)"
    else
        log_error "✗ Log directory has incorrect ownership: $owner"
        ((errors++))
    fi
    
    return $errors
}

test_root_access() {
    log_info "Testing root access from different hosts..."
    
    local errors=0
    
    # Test localhost access
    if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "✓ Root can connect from localhost (no password)"
    else
        log_warn "⚠ Root requires password for localhost connection"
    fi
    
    # Test with credentials if available
    if [ -f "/root/whmcs-db-credentials.txt" ]; then
        source /root/whmcs-db-credentials.txt
        if [ -n "$MYSQL_ROOT_PASS" ]; then
            if mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT 1;" >/dev/null 2>&1; then
                log_success "✓ Root can connect with saved password"
            else
                log_error "✗ Root cannot connect with saved password"
                ((errors++))
            fi
        fi
    fi
    
    return $errors
}

test_database_creation() {
    log_info "Testing database and user creation..."
    
    local errors=0
    
    if [ -f "/root/whmcs-db-credentials.txt" ]; then
        source /root/whmcs-db-credentials.txt
        
        # Test database exists
        if mysql -u root -p${MYSQL_ROOT_PASS} -e "USE ${DB_NAME}; SELECT 1;" >/dev/null 2>&1; then
            log_success "✓ WHMCS database exists and accessible"
        else
            log_error "✗ WHMCS database not accessible"
            ((errors++))
        fi
        
        # Test user can connect
        if mysql -u ${DB_USER} -p${DB_PASS} -e "USE ${DB_NAME}; SELECT 1;" >/dev/null 2>&1; then
            log_success "✓ WHMCS user can connect to database"
        else
            log_error "✗ WHMCS user cannot connect to database"
            ((errors++))
        fi
        
        # Test charset
        local charset=$(mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT DEFAULT_CHARACTER_SET_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME='${DB_NAME}';" -s -N 2>/dev/null)
        if [ "$charset" = "utf8mb4" ]; then
            log_success "✓ Database has correct charset (utf8mb4)"
        else
            log_error "✗ Database has incorrect charset: $charset"
            ((errors++))
        fi
        
    else
        log_warn "⚠ No WHMCS credentials found, skipping database tests"
    fi
    
    return $errors
}

test_binary_logging() {
    log_info "Testing binary logging configuration..."
    
    local errors=0
    
    # Check if binary logging is enabled
    if [ -f "/root/whmcs-db-credentials.txt" ]; then
        source /root/whmcs-db-credentials.txt
        
        local binlog_status=$(mysql -u root -p${MYSQL_ROOT_PASS} -e "SHOW VARIABLES LIKE 'log_bin';" -s -N 2>/dev/null | awk '{print $2}')
        if [ "$binlog_status" = "ON" ]; then
            log_success "✓ Binary logging is enabled"
        else
            log_error "✗ Binary logging is disabled"
            ((errors++))
        fi
        
        # Check binary log files
        local binlog_count=$(ls -1 /var/log/mysql/mysql-bin.* 2>/dev/null | wc -l)
        if [ "$binlog_count" -gt 0 ]; then
            log_success "✓ Binary log files exist ($binlog_count files)"
        else
            log_error "✗ No binary log files found"
            ((errors++))
        fi
        
    else
        log_warn "⚠ No credentials found, skipping binary log tests"
    fi
    
    return $errors
}

test_security_settings() {
    log_info "Testing security settings..."
    
    local errors=0
    
    if [ -f "/root/whmcs-db-credentials.txt" ]; then
        source /root/whmcs-db-credentials.txt
        
        # Check for anonymous users
        local anon_users=$(mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT COUNT(*) FROM mysql.user WHERE User='';" -s -N 2>/dev/null)
        if [ "$anon_users" = "0" ]; then
            log_success "✓ No anonymous users found"
        else
            log_error "✗ Anonymous users still exist ($anon_users)"
            ((errors++))
        fi
        
        # Check for test database
        local test_db=$(mysql -u root -p${MYSQL_ROOT_PASS} -e "SHOW DATABASES LIKE 'test';" -s -N 2>/dev/null | wc -l)
        if [ "$test_db" = "0" ]; then
            log_success "✓ Test database removed"
        else
            log_error "✗ Test database still exists"
            ((errors++))
        fi
        
        # Check root users
        local root_users=$(mysql -u root -p${MYSQL_ROOT_PASS} -e "SELECT User, Host FROM mysql.user WHERE User='root';" -s 2>/dev/null)
        log_info "Root users found:"
        echo "$root_users" | while read user host; do
            echo "  - $user@$host"
        done
        
    else
        log_warn "⚠ No credentials found, skipping security tests"
    fi
    
    return $errors
}

show_summary() {
    local total_errors=$1
    
    echo
    echo "=================================="
    echo "         TEST SUMMARY"
    echo "=================================="
    
    if [ $total_errors -eq 0 ]; then
        log_success "All tests passed! MariaDB is properly configured."
    else
        log_error "Found $total_errors issue(s). Please review the output above."
    fi
    
    echo
    echo "Next steps:"
    echo "1. If tests failed, run the appropriate fix commands:"
    echo "   ./fix-mariadb.sh fix-root"
    echo "   ./fix-mariadb.sh fix-binlog"
    echo "   ./fix-mariadb.sh secure"
    echo "   ./fix-mariadb.sh create-db"
    echo
    echo "2. If tests passed, continue with WHMCS installation:"
    echo "   ./whmcs-setup.sh app"
    echo
}

# Main execution
main() {
    echo "MariaDB Configuration Test Suite"
    echo "================================"
    echo
    
    local total_errors=0
    
    # Run all tests
    test_mariadb_service || ((total_errors++))
    echo
    
    test_log_directories || ((total_errors += $?))
    echo
    
    test_root_access || ((total_errors += $?))
    echo
    
    test_database_creation || ((total_errors += $?))
    echo
    
    test_binary_logging || ((total_errors += $?))
    echo
    
    test_security_settings || ((total_errors += $?))
    echo
    
    show_summary $total_errors
    
    exit $total_errors
}

# Run main function
main "$@"
