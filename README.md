# WHMCS Installation Scripts

Kumpulan script untuk instalasi WHMCS dengan arsitektur terpisah antara database server dan application server, dileng<PERSON><PERSON> dengan optimasi performa tinggi.

## 📋 Daftar Script

1. **`whmcs-setup.sh`** - Script utama instalasi WHMCS
2. **`whmcs-db-tuning.sh`** - Script optimasi database lanjutan
3. **`whmcs-app-tuning.sh`** - Script optimasi application server lanjutan
4. **`readydedis.com.conf`** - Contoh konfigurasi Nginx untuk WHMCS

## 🚀 Instalasi Cepat

### Persiapan

1. **Edit konfigurasi** di `whmcs-setup.sh`:
   ```bash
   DB_HOST="********"    # IP Database Server
   APP_HOST="********"   # IP Application Server  
   DOMAIN="yourdomain.com"
   ```

2. **Buat script executable**:
   ```bash
   chmod +x *.sh
   ```

### Instalasi Database Server

```bash
# Di server database
./whmcs-setup.sh db

# Optimasi database (opsional)
./whmcs-db-tuning.sh all
```

### Instalasi Application Server

```bash
# Copy file kredensial dari database server
scp root@DB_SERVER:/root/whmcs-db-credentials.txt /root/

# Di server aplikasi
./whmcs-setup.sh app

# Optimasi aplikasi (opsional)
./whmcs-app-tuning.sh all
```

### Upload WHMCS

```bash
# Upload file WHMCS ke /var/www/whmcs
# Set permission yang benar
chown -R www-data:www-data /var/www/whmcs
chmod -R 755 /var/www/whmcs
chmod -R 777 /var/www/whmcs/templates_c
chmod -R 777 /var/www/whmcs/downloads
chmod -R 777 /var/www/whmcs/attachments
```

## 🔧 Fitur Optimasi

### Database Server Optimizations

- **MariaDB Configuration**:
  - InnoDB buffer pool sizing otomatis berdasarkan RAM
  - Query cache optimization untuk read-heavy workload
  - Connection pooling dan thread management
  - Binary logging untuk backup/replication
  - Slow query logging untuk monitoring

- **Performance Features**:
  - Adaptive hash index
  - Multiple buffer pool instances
  - Optimized I/O settings
  - Memory-mapped file access

### Application Server Optimizations

- **PHP 8.1 dengan OPcache**:
  - JIT compilation enabled
  - Memory optimization
  - Preloading support
  - Session storage via Redis

- **Nginx HTTP/2**:
  - FastCGI caching
  - Gzip compression
  - Static file caching dengan long expiry
  - Rate limiting untuk security
  - SSL/TLS hardening

- **Redis & Memcached**:
  - Session storage
  - Application caching
  - Memory management

- **System Optimizations**:
  - Kernel network parameters
  - File descriptor limits
  - Memory management tuning

## 📊 Monitoring & Maintenance

### Database Monitoring

```bash
# Monitor performa database
/usr/local/bin/whmcs-db-monitor

# Optimasi tabel database
/usr/local/bin/whmcs-db-optimize

# Backup database
/usr/local/bin/whmcs-db-backup
```

### Application Monitoring

```bash
# Monitor performa aplikasi
/usr/local/bin/whmcs-app-monitor

# Clear semua cache
/usr/local/bin/whmcs-clear-cache
```

### Automated Tasks

Script akan mengatur cron jobs otomatis:

- **Daily backup** database (2 AM)
- **Weekly optimization** database (Sunday 3 AM)
- **Log rotation** untuk semua service

## 🔒 Security Features

- **Firewall Configuration**:
  - Database server: hanya allow koneksi dari app server
  - Application server: hanya port 22, 80, 443

- **Fail2ban Protection** (jika terinstall):
  - Login attempt protection
  - Admin area protection
  - Rate limiting

- **File Permissions**:
  - Secure default permissions
  - Writable directories hanya yang diperlukan
  - Hidden sensitive files

- **SSL/TLS Hardening**:
  - Modern cipher suites
  - HSTS headers
  - Security headers

## 📁 Struktur File

```
/var/www/whmcs/          # WHMCS installation directory
/var/log/nginx/          # Nginx logs
/var/log/php/            # PHP error logs
/var/backups/whmcs/      # Database backups
/etc/ssl/                # SSL certificates location
```

## ⚙️ Konfigurasi Lanjutan

### SSL Certificate Setup

```bash
# Letakkan certificate di:
/etc/ssl/certs/whmcs.crt
/etc/ssl/private/whmcs.key

# Update Nginx config jika perlu
nano /etc/nginx/sites-available/whmcs
```

### Database Tuning Manual

Edit `/etc/mysql/mariadb.conf.d/99-whmcs-optimizations.cnf` untuk fine-tuning:

```ini
# Sesuaikan dengan kebutuhan
innodb_buffer_pool_size = 2G
query_cache_size = 512M
max_connections = 300
```

### PHP Tuning Manual

Edit `/etc/php/8.1/fpm/pool.d/whmcs.conf`:

```ini
# Sesuaikan dengan traffic
pm.max_children = 100
pm.start_servers = 20
```

## 🚨 Troubleshooting

### Database Connection Issues

```bash
# Check MariaDB status
systemctl status mariadb

# Check firewall
ufw status

# Test connection from app server
mysql -h DB_HOST -u DB_USER -p
```

### Application Issues

```bash
# Check PHP-FPM
systemctl status php8.1-fpm

# Check Nginx
systemctl status nginx
nginx -t

# Check logs
tail -f /var/log/php/whmcs-error.log
tail -f /var/log/nginx/whmcs-error.log
```

### Performance Issues

```bash
# Monitor resources
htop
iotop

# Check database performance
/usr/local/bin/whmcs-db-monitor

# Check application performance  
/usr/local/bin/whmcs-app-monitor
```

## 📈 Benchmark Results

Dengan optimasi ini, Anda dapat mengharapkan:

- **Database**: 5-10x peningkatan query performance
- **Application**: 3-5x peningkatan response time
- **Caching**: 80-90% cache hit ratio
- **Concurrent Users**: Support 500+ concurrent users

## 🔄 Update & Maintenance

### Regular Maintenance

```bash
# Weekly - jalankan optimasi database
/usr/local/bin/whmcs-db-optimize

# Monthly - check dan update system
apt update && apt upgrade

# Quarterly - review dan tune configuration
```

### Backup Strategy

- **Database**: Daily automated backup dengan retention 30 hari
- **Files**: Setup rsync atau backup solution untuk /var/www/whmcs
- **Configuration**: Backup semua config files di /etc/

## 📞 Support

Untuk pertanyaan atau issue:

1. Check log files terlebih dahulu
2. Jalankan monitoring scripts
3. Review konfigurasi yang telah dimodifikasi
4. Konsultasi dengan dokumentasi WHMCS official

## 📝 Changelog

- **v1.0**: Initial release dengan basic optimization
- **v1.1**: Added advanced tuning scripts
- **v1.2**: Enhanced security features
- **v1.3**: Improved monitoring capabilities
