server {
	listen 80 default_server;
	server_name readydedis.com www.readydedis.com;
	return 301 https://readydedis.com$request_uri;
}

# HTTPS server
#
server {
	listen 443;
	server_name readydedis.com;
	root /var/www/html/public;
        access_log /var/log/nginx/readydedis.com-access_log;
        error_log /var/log/nginx/readydedis.com-error_log;

	add_header X-Frame-Options "SAMEORIGIN";
	add_header X-XSS-Protection "1; mode=block";
	add_header X-Content-Type-Options "nosniff";
	add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

	index index.php index.html index.htm;

	charset utf-8;

	ssl on;
	ssl_certificate /var/www/html/storage/ssl/cert.pem;
	ssl_certificate_key /var/www/html/storage/ssl/cert.key;

	ssl_session_timeout 5m;

	ssl_protocols SSLv3 TLSv1 TLSv1.1 TLSv1.2;
	ssl_ciphers "HIGH:!aNULL:!MD5 or HIGH:!aNULL:!MD5:!3DES";
	ssl_prefer_server_ciphers on;


	location ~ /clients/admin/(client!\.php|client/(.*)|table/(.*)|search!\.php|search/(.*)|apps|billing|setup|user|services|addons|domains|utilitiesemailmarketer!\.php|utilities/(.*)|logs|help!\.php|help/license|modules|image/(recent|upload)|validation_com/(.*))/?(.*)$ {
		rewrite ^/(.*)$ /clients/admin/index.php?rp=/admin/$1/$2;
	}

	location ~ /clients/(images/em|invoice|login|password|account|store|download|knowledgebase|announcements|clientarea/ssl-certificates|user/(verification|accounts|profile|password|security|verify)|cart/(domain/renew)|domain/pricing|cart/order|images/kb)/?(.*)$ {
                rewrite ^/(.*)$ /clients/index.php?rp=/$1/$2;
	}

	location / {
		try_files $uri $uri/ /index.php?$query_string;
	}
	
	location = /favicon.ico { access_log off; log_not_found off; }
	location = /robots.txt  { access_log off; log_not_found off; }
	
	error_page 404 /index.php;


	proxy_send_timeout 300s;
	proxy_read_timeout 300s;

	
	location ~ \.php$ {
		fastcgi_pass unix:/var/run/php/php7.3-fpm.sock;
		fastcgi_send_timeout 300;
	        fastcgi_read_timeout 300;
		fastcgi_index index.php;
		fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
		include fastcgi_params;
	}
	
	location ~ /\.(?!well-known).* {
		deny all;
	}

	
	location ^~ /clients/vendor/ {
	        deny all;
		return 403;
	}
	
	location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc|svg|woff|woff2|ttf)\$ {
		expires 1M;
		access_log off;
	        add_header Cache-Control "public";
	}
	
	location ~* \.(?:css|js)\$ {
	        expires 7d;
	        access_log off;
	        add_header Cache-Control "public";
	}
	
	location ~ /\.ht {
		deny  all;
	}
}
