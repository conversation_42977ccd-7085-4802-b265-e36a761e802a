#!/bin/bash

# WHMCS Application Server Performance Tuning Script
# This script provides additional optimizations for the WHMCS application server
# Run this after the main installation to fine-tune performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PHP_VERSION="8.1"
WEBROOT="/var/www/whmcs"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

get_system_memory() {
    local mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_mb=$((mem_kb / 1024))
    echo $mem_mb
}

optimize_php_fpm() {
    log_info "Optimizing PHP-FPM configuration based on system resources..."
    
    local total_mem=$(get_system_memory)
    local cpu_cores=$(nproc)
    
    # Calculate optimal PHP-FPM settings
    local max_children=$((total_mem / 64))  # Assuming 64MB per PHP process
    local start_servers=$((max_children / 4))
    local min_spare_servers=$((start_servers / 2))
    local max_spare_servers=$((start_servers * 2))
    
    # Cap values to reasonable limits
    if [ $max_children -gt 200 ]; then
        max_children=200
    elif [ $max_children -lt 20 ]; then
        max_children=20
    fi
    
    if [ $start_servers -lt 5 ]; then
        start_servers=5
    fi
    
    if [ $min_spare_servers -lt 2 ]; then
        min_spare_servers=2
    fi
    
    log_info "System Memory: ${total_mem}MB, CPU Cores: ${cpu_cores}"
    log_info "PHP-FPM max_children: ${max_children}"
    
    # Backup existing configuration
    cp /etc/php/${PHP_VERSION}/fpm/pool.d/whmcs.conf /etc/php/${PHP_VERSION}/fpm/pool.d/whmcs.conf.backup
    
    # Create optimized PHP-FPM configuration
    cat > /etc/php/${PHP_VERSION}/fpm/pool.d/whmcs.conf <<EOF
[whmcs]
user = www-data
group = www-data
listen = /run/php/php${PHP_VERSION}-fpm-whmcs.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660
listen.backlog = 511

# Process management optimized for system resources
pm = dynamic
pm.max_children = ${max_children}
pm.start_servers = ${start_servers}
pm.min_spare_servers = ${min_spare_servers}
pm.max_spare_servers = ${max_spare_servers}
pm.max_requests = 1000
pm.process_idle_timeout = 60s

# Status and monitoring
pm.status_path = /status
ping.path = /ping
ping.response = pong

# PHP settings optimized for WHMCS
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M
php_admin_value[max_input_vars] = 3000
php_admin_value[max_file_uploads] = 50

# Session configuration
php_admin_value[session.save_handler] = redis
php_admin_value[session.save_path] = "tcp://127.0.0.1:6379"
php_admin_value[session.gc_maxlifetime] = 7200
php_admin_value[session.cookie_lifetime] = 0
php_admin_value[session.cookie_secure] = 1
php_admin_value[session.cookie_httponly] = 1

# Security settings
php_admin_value[expose_php] = Off
php_admin_value[allow_url_fopen] = On
php_admin_value[allow_url_include] = Off
php_admin_value[disable_functions] = "exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source"

# Error logging
php_admin_value[log_errors] = On
php_admin_value[error_log] = /var/log/php/whmcs-error.log
php_admin_value[display_errors] = Off
php_admin_value[display_startup_errors] = Off

# Performance settings
php_admin_value[realpath_cache_size] = 4096k
php_admin_value[realpath_cache_ttl] = 600

# Environment variables
env[HOSTNAME] = \$HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp
EOF

    log_info "PHP-FPM configuration optimized"
}

optimize_nginx() {
    log_info "Optimizing Nginx configuration..."
    
    local cpu_cores=$(nproc)
    local worker_connections=2048
    
    # Backup existing configuration
    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
    
    # Create optimized Nginx configuration
    cat > /etc/nginx/nginx.conf <<EOF
user www-data;
worker_processes ${cpu_cores};
worker_rlimit_nofile 65535;
pid /run/nginx.pid;

events {
    worker_connections ${worker_connections};
    use epoll;
    multi_accept on;
}

http {
    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer settings
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeout settings
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;
    
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging Settings
    log_format main '\$remote_addr - \$remote_user [\$time_local] "\$request" '
                    '\$status \$body_bytes_sent "\$http_referer" '
                    '"\$http_user_agent" "\$http_x_forwarded_for" '
                    'rt=\$request_time uct="\$upstream_connect_time" '
                    'uht="\$upstream_header_time" urt="\$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;
    
    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_min_length 1000;
    gzip_disable "msie6";
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    
    # SSL Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # FastCGI cache
    fastcgi_cache_path /var/cache/nginx/fastcgi levels=1:2 keys_zone=WHMCS:100m inactive=60m;
    fastcgi_cache_key "\$scheme\$request_method\$host\$request_uri";
    
    # Include site configurations
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF

    # Create cache directory
    mkdir -p /var/cache/nginx/fastcgi
    chown -R www-data:www-data /var/cache/nginx
    
    log_info "Nginx configuration optimized"
}

setup_monitoring() {
    log_info "Setting up application monitoring scripts..."
    
    # Create PHP-FPM monitoring script
    cat > /usr/local/bin/whmcs-app-monitor <<'EOF'
#!/bin/bash
# WHMCS Application Performance Monitor

echo "=== WHMCS Application Performance Report ==="
echo "Generated: $(date)"
echo

# System resources
echo "=== System Resources ==="
echo "CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5}'
echo
echo "Memory Usage:"
free -h
echo
echo "Disk Usage:"
df -h /var/www/whmcs
echo

# PHP-FPM status
echo "=== PHP-FPM Status ==="
if systemctl is-active --quiet php8.1-fpm; then
    echo "PHP-FPM: Running"
    
    # Get PHP-FPM pool status
    curl -s http://localhost/status?full | head -20
else
    echo "PHP-FPM: Not running"
fi
echo

# Nginx status
echo "=== Nginx Status ==="
if systemctl is-active --quiet nginx; then
    echo "Nginx: Running"
    
    # Show active connections
    ss -tuln | grep :80
    ss -tuln | grep :443
else
    echo "Nginx: Not running"
fi
echo

# Redis status
echo "=== Redis Status ==="
if systemctl is-active --quiet redis-server; then
    echo "Redis: Running"
    redis-cli info memory | grep used_memory_human
    redis-cli info stats | grep total_commands_processed
else
    echo "Redis: Not running"
fi
echo

# Check log files for errors
echo "=== Recent Errors ==="
echo "PHP Errors (last 10):"
tail -10 /var/log/php/whmcs-error.log 2>/dev/null || echo "No PHP errors found"
echo
echo "Nginx Errors (last 5):"
tail -5 /var/log/nginx/whmcs-error.log 2>/dev/null || echo "No Nginx errors found"
EOF

    chmod +x /usr/local/bin/whmcs-app-monitor
    
    # Create cache clearing script
    cat > /usr/local/bin/whmcs-clear-cache <<'EOF'
#!/bin/bash
# WHMCS Cache Clearing Script

echo "Clearing WHMCS caches..."

# Clear OPcache
echo "Clearing OPcache..."
php -r "opcache_reset();" 2>/dev/null || echo "OPcache not available"

# Clear Nginx FastCGI cache
echo "Clearing Nginx FastCGI cache..."
rm -rf /var/cache/nginx/fastcgi/*

# Clear Redis cache (be careful with this in production)
echo "Flushing Redis cache..."
redis-cli FLUSHDB

# Clear WHMCS template cache if directory exists
if [ -d "/var/www/whmcs/templates_c" ]; then
    echo "Clearing WHMCS template cache..."
    rm -rf /var/www/whmcs/templates_c/*
fi

# Restart PHP-FPM to ensure clean state
echo "Restarting PHP-FPM..."
systemctl restart php8.1-fpm

# Reload Nginx
echo "Reloading Nginx..."
systemctl reload nginx

echo "Cache clearing completed!"
EOF

    chmod +x /usr/local/bin/whmcs-clear-cache
    
    # Create log rotation optimization
    cat > /etc/logrotate.d/whmcs-app <<EOF
/var/log/php/whmcs*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    sharedscripts
    postrotate
        systemctl reload php8.1-fpm
    endscript
}

/var/log/nginx/whmcs*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 0644 www-data adm
    sharedscripts
    postrotate
        systemctl reload nginx
    endscript
}
EOF

    log_info "Monitoring scripts created:"
    log_info "  - /usr/local/bin/whmcs-app-monitor (application performance monitoring)"
    log_info "  - /usr/local/bin/whmcs-clear-cache (clear all caches)"
}

setup_security_hardening() {
    log_info "Applying additional security hardening..."
    
    # Create fail2ban configuration for WHMCS
    if command -v fail2ban-server >/dev/null 2>&1; then
        cat > /etc/fail2ban/jail.d/whmcs.conf <<EOF
[whmcs-auth]
enabled = true
port = http,https
filter = whmcs-auth
logpath = /var/log/nginx/whmcs-access.log
maxretry = 5
bantime = 3600
findtime = 600

[whmcs-admin]
enabled = true
port = http,https
filter = whmcs-admin
logpath = /var/log/nginx/whmcs-access.log
maxretry = 3
bantime = 7200
findtime = 300
EOF

        # Create fail2ban filters
        cat > /etc/fail2ban/filter.d/whmcs-auth.conf <<EOF
[Definition]
failregex = ^<HOST> -.*POST.*(login|dologin|password).*HTTP/1.* 200
ignoreregex =
EOF

        cat > /etc/fail2ban/filter.d/whmcs-admin.conf <<EOF
[Definition]
failregex = ^<HOST> -.*GET.*/admin.*HTTP/1.* 404
            ^<HOST> -.*POST.*/admin.*HTTP/1.* 403
ignoreregex =
EOF

        systemctl restart fail2ban
        log_info "Fail2ban configured for WHMCS protection"
    else
        log_warn "Fail2ban not installed, skipping security rules"
    fi
    
    # Set proper file permissions for WHMCS
    if [ -d "$WEBROOT" ]; then
        log_info "Setting secure file permissions for WHMCS..."
        find $WEBROOT -type f -exec chmod 644 {} \;
        find $WEBROOT -type d -exec chmod 755 {} \;
        
        # Writable directories for WHMCS
        chmod -R 777 $WEBROOT/templates_c 2>/dev/null || true
        chmod -R 777 $WEBROOT/downloads 2>/dev/null || true
        chmod -R 777 $WEBROOT/attachments 2>/dev/null || true
        
        chown -R www-data:www-data $WEBROOT
        log_info "File permissions set securely"
    fi
}

# Main execution
case "$1" in
    "php")
        check_root
        optimize_php_fpm
        systemctl restart php${PHP_VERSION}-fpm
        log_info "PHP-FPM optimized and restarted"
        ;;
    "nginx")
        check_root
        optimize_nginx
        nginx -t && systemctl restart nginx
        log_info "Nginx optimized and restarted"
        ;;
    "monitor")
        check_root
        setup_monitoring
        log_info "Monitoring scripts configured"
        ;;
    "security")
        check_root
        setup_security_hardening
        log_info "Security hardening applied"
        ;;
    "all")
        check_root
        optimize_php_fpm
        optimize_nginx
        setup_monitoring
        setup_security_hardening
        nginx -t && systemctl restart nginx
        systemctl restart php${PHP_VERSION}-fpm
        log_info "Complete application tuning applied"
        ;;
    *)
        echo "WHMCS Application Server Tuning Script"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  php      - Optimize PHP-FPM configuration"
        echo "  nginx    - Optimize Nginx configuration"
        echo "  monitor  - Setup monitoring scripts"
        echo "  security - Apply security hardening"
        echo "  all      - Apply all optimizations"
        echo ""
        echo "Example:"
        echo "  $0 all"
        exit 1
        ;;
esac
